# 04-信息收集-决策阶段

> **文档性质**：通用决策阶段模板-适用于任何领域的决策支持
> **创建时间**：2025-08-04
> **核心使命**：将前三阶段的整合分析成果转换为具体的决策选择和可执行行动计划
> **质量标准**：深度分析优先，确保决策的科学性和可靠性
> **通用性原则**：继承前三阶段建立的比喻体系和设计风格，保持整体连贯性

---

# 📖 第一部分：模板概述与使命

## 🎯 核心使命定义

### 决策支持的根本目标
将前三阶段收集的丰富信息和深度分析，转换为用户可以立即执行的具体决策和行动方案。这不是简单的信息汇总，而是智慧的升华过程——从"知道有哪些选择"到"明确选择什么"再到"知道如何行动"。

### 价值创造的三重转换
1. **信息→洞察**：将海量信息转换为决策洞察
2. **洞察→选择**：将分析洞察转换为明确选择
3. **选择→行动**：将决策选择转换为执行计划

## 🔗 与前三阶段的关系

### 数据传导链条
```
01-方向阶段 → 立体化信息收集 → 发展方向和趋势
02-权威阶段 → 权威观点验证 → 可信的专业建议
03-整合阶段 → 智慧整合分析 → 可执行路径清单
04-决策阶段 → 决策支持转换 → 具体选择和行动计划
```

### 继承与创新
- **继承**：前三阶段建立的比喻体系、设计风格、分析深度
- **创新**：从分析转向决策，从理解转向行动，从可能转向确定

## 📋 适用范围与标准

### 适用场景
- 重要的人生决策（职业发展、学习方向、投资选择）
- 复杂的商业决策（战略选择、产品方向、市场进入）
- 需要深度分析的任何决策场景

### 执行标准
- **深度优先**：宁可慢一些，也要确保分析的深度和质量
- **数据驱动**：所有决策建议都要有明确的数据和逻辑支撑
- **个性化适配**：充分考虑用户的具体情境和约束条件
- **可执行性**：最终输出必须是可立即执行的具体行动计划

---

# 🎭 第二部分：决策支持体验设计

## 🏗️ 三层决策建筑比喻

### 决策支持中心的整体设计
想象您来到一座专业的**决策支持中心**，这是一座透明的现代化建筑，分为三个功能层次，每一层都为您的决策增加不同的价值。

### 建筑的立体结构
```
                    🎯 决策支持中心
                         ┌─────────┐
                    ┌────┤ 第3层   ├────┐
                    │    │行动转换 │    │
                    │    └─────────┘    │
               ┌────┴────┐         ┌────┴────┐
               │ 第2层   │         │ 输出级  │
               │情境适配 │◄────────┤ 高级    │
               └─────────┘         └─────────┘
                    │
               ┌────┴────┐         ┌─────────┐
               │ 第1层   │         │ 输出级  │
               │客观评估 │◄────────┤ 标准级  │
               └─────────┘         └─────────┘
                    │
               ┌────┴────┐         ┌─────────┐
               │ 输入层  │         │ 输出级  │
               │前三阶段 │◄────────┤ 基础级  │
               └─────────┘         └─────────┘
```

## 🌈 三层体验的感官设计

### 📊 第1层-客观评估层
**环境氛围**：现代化的数据分析中心
- **视觉**：冷静的蓝色主调，大屏幕显示各种分析图表
- **听觉**：轻柔的键盘敲击声，偶尔的专业讨论声
- **触觉**：精密仪器的金属质感，科学严谨的氛围
- **工作场景**：数据科学家用精密的天平称量每个选择的重量
- **核心感受**：理性、客观、可信赖

### 🎯 第2层-情境适配层
**环境氛围**：温馨的个人咨询室
- **视觉**：温暖的橙色主调，舒适的沙发和个性化装饰
- **听觉**：温和的对话声，理解性的"嗯嗯"声
- **触觉**：温暖的木质质感，舒适的布艺触感
- **工作场景**：经验丰富的顾问认真倾听，将客观分析转换为"最适合您"的建议
- **核心感受**：被理解、被关怀、个性化

### � 第3层-行动转换层
**环境氛围**：高效的项目管理办公室
- **视觉**：活力的绿色主调，墙面贴满时间表和执行计划
- **听觉**：快速的讨论声，充满活力的"好的，就这样做"
- **触觉**：光滑的塑料质感，充满活力的按钮触感
- **工作场景**：执行专家快速制定详细计划，告诉您"明天就可以开始做什么"
- **核心感受**：有方向、有动力、立即可行动

## � 决策温度的渐进变化

### 温度变化的体验
- **第1层的理性温度**：冷静客观，如精密仪器般准确
- **第2层的人性温度**：温暖贴心，如朋友般理解
- **第3层的行动温度**：充满活力，如教练般激励

### 完整的决策旅程
用户的体验就像乘坐电梯从底层到顶层，每一层都为决策增加不同的价值，最终离开时手中拿着完整的行动指南，知道"明天就可以开始做什么"。

---

# 🔧 第三部分：执行操作系统

## 📋 第0步：强制输入信息理解（执行基础）

### 核心原则
在开始任何决策分析前，必须先完成对前三阶段报告的深度理解。这不是可选步骤，而是确保决策质量的根本保障。

### 🔍 信息理解的三重检查

#### 第一重：报告完整性检查
```
✅ 01-[领域名称]-方向阶段报告：完整获取
✅ 02-[领域名称]-权威阶段报告：完整获取
✅ 03-[领域名称]-整合分析报告：完整获取
✅ 比喻体系和设计风格：准确识别
✅ 数据传导关系：清晰理解
```

#### 第二重：关键信息提取
**从01-方向阶段提取**：
- 立体化信息架构的具体设计
- 核心发展方向和重要趋势
- 使用的比喻体系和设计风格

**从02-权威阶段提取**：
- 权威人物和机构的具体观点
- 已验证的事实和存在争议的观点
- 权威推荐的路径和注意事项

**从03-整合阶段提取**：
- 所有可执行路径的详细信息
- 每个路径的实施步骤和资源需求
- 整合分析的核心结论和建议

#### 第三重：理解准确性确认
向用户确认对输入信息的理解是否准确，只有获得确认后才能进入决策分析阶段。

### ⚠️ 执行约束
**绝对禁止**：
- 跳过信息理解阶段
- 基于抽象框架而非具体报告
- 编造或假设不存在的信息

**强制要求**：
- 逐个深度阅读所有报告
- 提取具体而非抽象的信息
- 确保所有分析都有信息支撑

## 🎯 三层决策执行流程

### 执行流程总览
```
输入信息理解 → 第1层客观评估 → 第2层情境适配 → 第3层行动转换 → 决策报告输出
```

### � 第1层：客观评估层执行

#### 执行目标
基于前三阶段的具体信息，对所有可执行路径进行科学、客观的量化评估。

#### 执行步骤
1. **路径收集**：从第3阶段报告中提取所有可执行路径
2. **维度设定**：确定五个评估维度的具体标准
3. **量化评分**：对每个路径进行1-5分的详细评分
4. **对比分析**：生成路径对比矩阵和客观排名
5. **结论输出**：提供基于数据的推荐和理由

#### 关键操作词
- "路径评估"、"客观分析"、"多维度评估"
- 使用"多维评估框架"进行系统评分
- 生成"路径对比矩阵"显示优劣势
- 输出"客观评估结论"和推荐排序

### 🎯 第2层：情境适配层执行

#### 执行目标
将第1层的客观评估结果与用户的具体情境相结合，生成个性化的决策建议。

#### 执行步骤
1. **情境收集**：深度了解用户的背景、目标、约束、偏好
2. **情境分析**：分析用户的具体情况和需求特点
3. **匹配分析**：将客观评估与用户情境进行匹配
4. **建议生成**：生成针对用户的个性化决策建议
5. **风险提醒**：识别用户需要特别注意的风险点

#### 关键操作词
- "情境分析"、"个性化建议"、"用户适配"
- 执行"用户情境收集"获取个人信息
- 进行"情境匹配分析"结合客观评估
- 生成"个性化决策建议"和选择理由

### � 第3层：行动转换层执行

#### 执行目标
将第2层的决策建议转换为详细的、可立即执行的行动计划。

#### 执行步骤
1. **计划制定**：基于决策建议制定详细执行计划
2. **时间规划**：设定合理的时间安排和执行节奏
3. **里程碑设定**：确定关键节点和验证标准
4. **风险预案**：制定应急处理方案和应对策略
5. **行动指导**：提供立即可执行的下一步行动

#### 关键操作词
- "行动计划"、"执行规划"、"时间线制定"
- 制定"详细时间线"和执行步骤
- 设定"关键里程碑"和验证标准
- 准备"风险预案"和应急措施

## 🔍 质量检查机制

### 过程质量检查
- **输入质量**：是否完整理解了前三阶段报告？
- **评估质量**：是否基于明确标准和数据进行评估？
- **适配质量**：是否充分考虑了用户的具体情境？
- **执行质量**：是否提供了可立即执行的具体计划？

### 输出质量标准
- 决策建议有明确的选择和理由
- 成本效益分析详细且量化
- 行动计划具体到可立即执行
- 整体输出与前三阶段保持连贯

### 反馈改进机制
- 每个层次完成后获得用户确认
- 发现问题时及时调整优化
- 建立持续改进的迭代机制

---

## 🎭 情景形容：决策支持中心的立体体验

### 🏗️ 决策支持的立体建筑结构

**🎯 决策支持中心的可视化设计**：

```
                    🎯 决策支持中心
                         ┌─────────┐
                    ┌────┤ 第3层   ├────┐
                    │    │行动转换 │    │
                    │    └─────────┘    │
               ┌────┴────┐         ┌────┴────┐
               │ 第2层   │         │ 输出级  │
               │情境适配 │◄────────┤ 高级    │
               └─────────┘         └─────────┘
                    │
               ┌────┴────┐         ┌─────────┐
               │ 第1层   │         │ 输出级  │
               │客观评估 │◄────────┤ 标准级  │
               └─────────┘         └─────────┘
                    │
               ┌────┴────┐         ┌─────────┐
               │ 输入层  │         │ 输出级  │
               │前三阶段 │◄────────┤ 基础级  │
               └─────────┘         └─────────┘
```

### 🎭 决策支持中心的情景比喻

想象您走进一个专业的**决策支持中心**，这里就像一个现代化的智慧大厦：

**🏢 建筑整体感受**：
- 这是一座透明的玻璃建筑，您可以清楚地看到每一层的工作情况
- 建筑分为三个主要楼层，每层都有不同的专业团队
- 从底层到顶层，信息像电梯一样平稳传递，每层都在为您的决策增加价值

**📊 第1层-客观评估层**：
- **环境氛围**：像一个现代化的数据分析中心，到处都是图表和分析屏幕
- **工作人员**：穿着白大褂的数据科学家，手持计算器和分析报告
- **工作场景**：他们将您的多个选择放在精密的天平上，用科学的方法测量每个选项的重量
- **声音特色**：安静而专注，偶尔传来"这个数据很关键"的讨论声
- **给您的感受**：理性、客观、可信赖

**🎯 第2层-情境适配层**：
- **环境氛围**：像一个温馨的咨询室，有舒适的沙发和个性化的装饰
- **工作人员**：经验丰富的人生顾问，眼神温和，善于倾听
- **工作场景**：他们坐在您对面，认真听取您的个人情况，然后将第1层的客观分析转换为"最适合您"的建议
- **声音特色**：轻松而专业，充满"这个很适合您的情况"的贴心话语
- **给您的感受**：被理解、被关怀、个性化

**📋 第3层-行动转换层**：
- **环境氛围**：像一个高效的项目管理办公室，墙上贴满时间表和执行计划
- **工作人员**：精力充沛的执行专家，手持甘特图和行动清单
- **工作场景**：他们将第2层的决策建议快速转换为详细的行动计划，告诉您"明天就可以开始做什么"
- **声音特色**：充满活力，经常说"我们这样执行"、"下一步是这个"
- **给您的感受**：有方向、有动力、立即可行动

### 🌈 感官体验的具体描述

**👁️ 视觉体验**：
- **色彩设计**：第1层是冷静的蓝色（理性），第2层是温暖的橙色（人性），第3层是活力的绿色（行动）
- **光线效果**：从底层到顶层，光线逐渐从冷光变为暖光，最后变为明亮的自然光
- **空间布局**：开放式设计，您可以看到信息如何从一层流向另一层

**👂 听觉体验**：
- **第1层声音**：键盘敲击声、计算器按键声、轻声的数据讨论
- **第2层声音**：温和的对话声、翻页声、偶尔的理解性"嗯嗯"声
- **第3层声音**：快速的讨论声、纸张翻动声、充满活力的"好的，就这样做"

**🤚 触觉体验**：
- **第1层**：冷静的金属质感，精确的仪器触感
- **第2层**：温暖的木质质感，舒适的布艺触感
- **第3层**：光滑的塑料质感，充满活力的按钮触感

### 🔄 抽象概念的感知化转换

| 抽象概念 | 感知化表现 | 具体体验 |
|---------|-----------|---------|
| 多维评估 | 精密天平和测量仪器 | 看到选项被科学地称重和测量 |
| 情境适配 | 个性化咨询对话 | 感受到被理解和关怀的温暖 |
| 行动转换 | 执行计划制定过程 | 听到具体的行动指令和时间安排 |
| 决策支持 | 三层建筑的信息流动 | 看到信息如何层层加工和提升 |

### 🎯 决策支持体验总结

**🏗️ 三层建筑的完整体验**：
用户的决策体验就像乘坐电梯从底层到顶层：
- 在第1层获得科学客观的分析（理性的温度）
- 在第2层获得个性化的建议（人性的温度）
- 在第3层获得具体的行动方案（行动的温度）

每一层都为决策增加不同的价值，最终用户离开时手中拿着完整的行动指南，知道"明天就可以开始做什么"。

---

## 🔧 可操作化执行策略

### 🎯 三层建筑的系统性执行策略

**📊 第1层执行策略-客观评估层**：
- **执行方法**：多维度量化评估框架
- **操作步骤**：
  1. 收集前三阶段的所有可执行路径
  2. 建立5个核心评估维度（可行性、成本效益、风险、学习价值、执行难度）
  3. 对每个路径进行1-5分的量化评分
  4. 生成路径对比矩阵和排名
- **输出标准**：客观的路径评估报告和推荐排序

**🎯 第2层执行策略-情境适配层**：
- **执行方法**：个性化情境分析和决策建议
- **操作步骤**：
  1. 深度收集用户的个人情境信息
  2. 分析用户的背景、目标、约束条件、风险偏好
  3. 将第1层的客观评估与用户情境相匹配
  4. 生成个性化的决策建议和理由
- **输出标准**：针对用户具体情况的个性化决策建议

**📋 第3层执行策略-行动转换层**：
- **执行方法**：可执行行动计划制定
- **操作步骤**：
  1. 基于第2层的决策建议制定详细执行计划
  2. 设定时间线、里程碑和关键节点
  3. 识别风险点并制定应急预案
  4. 提供立即可执行的下一步行动
- **输出标准**：完整的行动计划和风险预案

### 🔑 精确的关键词操作指导

**🔑 第1层关键词操作**：
- **触发词**："路径评估"、"客观分析"、"多维度评估"
- **执行指令**：
  - 使用"多维评估框架"对所有路径进行评分
  - 生成"路径对比矩阵"显示各路径优劣势
  - 输出"客观评估结论"和推荐排序

**🔑 第2层关键词操作**：
- **触发词**："情境分析"、"个性化建议"、"用户适配"
- **执行指令**：
  - 执行"用户情境收集"获取个人信息
  - 进行"情境匹配分析"结合客观评估
  - 生成"个性化决策建议"和选择理由

**🔑 第3层关键词操作**：
- **触发词**："行动计划"、"执行规划"、"时间线制定"
- **执行指令**：
  - 制定"详细时间线"和执行步骤
  - 设定"关键里程碑"和验证标准
  - 准备"风险预案"和应急措施

### 🔄 详细执行步骤和流程

**完整执行流程**：
```
开始 → 第0步：输入信息理解 → 第1层：客观评估 → 第2层：情境适配 → 第3层：行动转换 → 输出决策报告
```

**📋 分层执行步骤**：

**第0步：输入信息理解**
1. 深度阅读01-03阶段报告
2. 提取所有可执行路径和关键信息
3. 识别前序阶段的比喻体系和设计风格
4. 确认对输入信息的理解准确性

**第1层：客观评估层执行**
1. **路径收集**：列出所有从第3阶段获得的可执行路径
2. **维度设定**：确定5个评估维度的具体标准
3. **量化评分**：对每个路径在每个维度进行1-5分评分
4. **对比分析**：生成路径对比矩阵和排名
5. **结论输出**：提供客观的路径推荐和理由

**第2层：情境适配层执行**
1. **信息收集**：收集用户的背景、目标、约束、偏好
2. **情境分析**：分析用户的具体情况和需求特点
3. **匹配分析**：将客观评估结果与用户情境相匹配
4. **建议生成**：生成个性化的决策建议
5. **风险提醒**：识别用户需要特别注意的风险点

**第3层：行动转换层执行**
1. **计划制定**：基于决策建议制定详细执行计划
2. **时间规划**：设定合理的时间线和执行节奏
3. **里程碑设定**：确定关键节点和验证标准
4. **风险预案**：制定应急处理方案
5. **行动指导**：提供立即可执行的下一步行动

### ✅ 可操作性保障机制

**可操作性检查标准**：

**第1层可操作性**：
- 每个评估维度都有明确的评分标准
- 每个路径都有具体的评分依据
- 对比矩阵格式标准化，易于理解
- 推荐结论有明确的数据支撑

**第2层可操作性**：
- 用户信息收集有具体的问题清单
- 情境分析有标准的分析框架
- 决策建议有明确的选择理由
- 风险提醒有具体的应对建议

**第3层可操作性**：
- 执行计划分解到具体的行动步骤
- 时间安排基于合理的评估方法
- 里程碑设定有明确的验证标准
- 风险预案有具体的触发条件和应对措施

### 🔍 质量检查和验证机制

**输入质量检查**：
- ✅ 是否完整理解了前三阶段的报告内容？
- ✅ 是否提取了所有可执行路径的详细信息？
- ✅ 是否继承了前序阶段的比喻体系和设计风格？

**过程质量检查**：
- ✅ 第1层评估是否基于明确的标准和数据？
- ✅ 第2层建议是否充分考虑了用户的具体情境？
- ✅ 第3层计划是否具体到可立即执行的程度？

**输出质量检查**：
- ✅ 决策建议是否有明确的选择和理由？
- ✅ 行动计划是否包含时间线、里程碑和风险预案？
- ✅ 整体输出是否与前三阶段保持连贯性？

**🔄 反馈和改进机制**：
- 每个层次完成后都要获得用户确认
- 发现问题时要及时调整和优化
- 建立持续改进的迭代机制

---

# 📊 第四部分：评估分析框架

## � 五维度评估体系

### 📊 第1层-客观评估层详细格式

#### 🔍 五维度评估框架（重点优化成本效益）

**维度2：成本效益评估** ⭐⭐⭐⭐⭐ **（核心维度）**

**📊 成本分析框架**：
```
💰 直接成本评估：
1. 资金成本分析：
   - 学习费用：[课程费用/书籍费用/培训费用]
   - 工具设备费用：[软件/硬件/平台费用]
   - 资源购买费用：[数据/服务/咨询费用]
   - 认证考试费用：[相关认证和考试费用]
   总计：[具体金额范围]

2. 时间成本分析：
   - 学习时间投入：[理论学习所需时间]
   - 实践时间投入：[动手实践所需时间]
   - 试错调试时间：[解决问题和调试时间]
   - 巩固提升时间：[技能巩固和提升时间]
   总计：[具体时间投入]

3. 精力成本分析：
   - 认知负荷：[学习新知识的脑力消耗]
   - 情感投入：[面对挫折和挑战的心理成本]
   - 体力消耗：[长时间学习和实践的体力成本]
   - 压力管理：[时间压力和成果压力的管理成本]

4. 机会成本分析：
   - 放弃的其他学习机会：[同时期可选择的其他发展路径]
   - 放弃的收入机会：[用于学习的时间本可获得的收入]
   - 放弃的社交机会：[因专注学习而减少的社交活动]
   - 放弃的休息时间：[压缩的休息和娱乐时间]
```

**📈 收益分析框架**：
```
🎯 短期收益（3-6个月）：
1. 技能提升收益：
   - 核心技能获得：[具体掌握的核心技能]
   - 辅助技能提升：[相关联技能的提升]
   - 技能认证价值：[获得的认证和证书价值]
   量化指标：[技能水平提升百分比]

2. 经济收益：
   - 薪资提升潜力：[预期薪资增长幅度]
   - 兼职收入机会：[新技能带来的兼职机会]
   - 项目收入可能：[独立项目的收入潜力]
   量化指标：[预期经济收益金额]

3. 经验积累收益：
   - 实践经验价值：[获得的实际操作经验]
   - 问题解决经验：[遇到和解决问题的经验]
   - 项目管理经验：[项目执行和管理经验]
   量化指标：[经验丰富度提升程度]

🚀 中期收益（6个月-2年）：
1. 职业发展收益：
   - 职位晋升机会：[新技能对职业晋升的促进]
   - 跨部门机会：[技能带来的跨领域发展机会]
   - 行业转换可能：[进入新行业的可能性]
   量化指标：[职业发展加速度]

2. 网络扩展收益：
   - 专业人脉拓展：[结识的专业人士数量和质量]
   - 行业资源获得：[获得的行业资源和信息渠道]
   - 合作机会增加：[潜在的合作和协作机会]
   量化指标：[人脉网络扩展程度]

3. 能力复合收益：
   - 技能组合优势：[新技能与现有技能的协同效应]
   - 跨界创新能力：[跨领域整合创新的能力]
   - 适应性提升：[面对变化的适应和学习能力]
   量化指标：[综合能力提升倍数]

🌟 长期收益（2年以上）：
1. 战略价值收益：
   - 核心竞争力构建：[在市场中的独特竞争优势]
   - 行业地位提升：[在行业中的影响力和地位]
   - 个人品牌价值：[个人专业品牌的建立和价值]
   量化指标：[市场竞争力指数]

2. 复利效应收益：
   - 技能复利增长：[技能水平的指数级增长]
   - 经验复利积累：[经验价值的复合增长]
   - 网络复利扩展：[人脉网络的指数级扩展]
   量化指标：[复利增长率]

3. 平台价值收益：
   - 未来发展平台：[为后续发展奠定的基础平台]
   - 资源整合能力：[整合和调动资源的能力]
   - 影响力扩散：[对他人和行业的影响力]
   量化指标：[平台价值系数]
```

**💰 投资回报率精确计算**：
```
📊 ROI计算模型：

1. 总成本计算：
   总成本 = 直接成本 + 时间成本 + 精力成本 + 机会成本
   - 直接成本：[具体金额]
   - 时间成本：[时间 × 时薪换算]
   - 精力成本：[精力投入 × 精力价值系数]
   - 机会成本：[放弃机会的价值]

2. 总收益计算：
   总收益 = 短期收益 + 中期收益 + 长期收益（折现值）
   - 短期收益：[3-6个月内的直接收益]
   - 中期收益：[6个月-2年的收益折现值]
   - 长期收益：[2年以上收益的现值]

3. ROI计算公式：
   ROI = (总收益 - 总成本) / 总成本 × 100%

4. 回报周期计算：
   回报周期 = 总成本 / 年化收益

5. 风险调整收益：
   风险调整ROI = ROI × (1 - 风险系数)
```

**📊 成本效益评分标准**：
```
✅ 高性价比（4-5分）：
- ROI > 200%
- 回报周期 < 1年
- 风险调整收益 > 150%
- 成本合理，收益丰厚

⚠️ 中等性价比（2-3分）：
- ROI 50%-200%
- 回报周期 1-3年
- 风险调整收益 50%-150%
- 投入与回报基本平衡

❌ 低性价比（0-1分）：
- ROI < 50%
- 回报周期 > 3年
- 风险调整收益 < 50%
- 投入过高，回报不足
```

## 📋 其他评估维度框架

### 维度1：可行性评估 ⭐⭐⭐⭐⭐

**评估标准**：
- **技术可行性**：所需技术是否成熟可获得？
- **资源可行性**：所需资源是否在可承受范围内？
- **时间可行性**：预期时间是否合理可实现？
- **能力可行性**：是否匹配当前能力水平？

**评分方法**：
- ✅ 高可行性（4-5分）：条件充分，实现概率高
- ⚠️ 中等可行性（2-3分）：存在一定挑战，需要努力
- ❌ 低可行性（0-1分）：条件不足，实现困难

### 维度3：风险评估 ⭐⭐⭐⭐

**风险识别框架**：
- **技术风险**：技术实现过程中的不确定性和难点
- **市场风险**：外部环境变化对项目的影响
- **个人风险**：对个人发展可能产生的负面影响
- **时间风险**：时间延误或错过时机的可能性

**评分方法**：
- ✅ 低风险（4-5分）：风险可控，影响有限
- ⚠️ 中等风险（2-3分）：存在风险，需要管理
- ❌ 高风险（0-1分）：风险较大，需要谨慎

### 维度4：学习成长价值 ⭐⭐⭐⭐

**成长价值评估**：
- **技能提升价值**：能够获得的新技能和能力
- **经验积累价值**：实践过程中的经验价值
- **认知升级价值**：思维方式和认知水平的提升
- **网络扩展价值**：人脉和资源网络的拓展

**评分方法**：
- ✅ 高价值（4-5分）：学习收获丰富，成长明显
- ⚠️ 中等价值（2-3分）：有一定学习价值
- ❌ 低价值（0-1分）：学习价值有限

### 维度5：执行难度 ⭐⭐⭐

**难度评估标准**：
- **操作复杂度**：执行步骤的复杂程度
- **协调难度**：需要协调的资源和人员
- **持续性要求**：长期坚持的难度
- **调整灵活性**：遇到问题时的调整空间

**评分方法**：
- ✅ 易执行（4-5分）：操作简单，容易坚持
- ⚠️ 中等难度（2-3分）：需要一定努力和坚持
- ❌ 高难度（0-1分）：执行复杂，坚持困难

## 📊 量化评分标准

### 路径对比矩阵模板

| 路径名称 | 可行性 | 成本效益 | 风险评估 | 学习价值 | 执行难度 | 综合得分 | 排名 |
|---------|--------|----------|----------|----------|----------|----------|------|
| 路径A   | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|
| 路径B   | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|
| 路径C   | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|

### 综合评分计算方法

**权重分配建议**：
- 成本效益：30%（核心维度）
- 可行性：25%
- 风险评估：20%
- 学习价值：15%
- 执行难度：10%

**计算公式**：
综合得分 = (可行性×0.25) + (成本效益×0.30) + (风险评估×0.20) + (学习价值×0.15) + (执行难度×0.10)

## 🔗 科学依据和双链连接

### 📚 重要说明

**本评估框架基于科学研究**：
所有评估方法、数据范围、计算公式都基于真实的科学研究，避免臆造数据。

**科学依据文档**：
- [[学习投资回报率科学研究/01-学习投资回报率-方向阶段报告]] - 理论基础
- [[学习投资回报率科学研究/02-学习投资回报率-权威阶段报告]] - 权威验证
- [[学习投资回报率科学研究/03-学习投资回报率-整合分析报告]] - 整合分析
- [[学习投资回报率科学研究/04-学习投资回报率-AI使用指南]] - AI使用指导

### 🎯 AI使用指导

**强制要求**：
- ✅ 使用前必须阅读 [[学习投资回报率科学研究/04-学习投资回报率-AI使用指南]]
- ✅ 所有评估都要基于科学研究文档的具体方法
- ✅ 避免编造具体数字，提供评估方法和范围
- ✅ 引用具体的研究依据和验证链接

**禁止行为**：
- ❌ 编造ROI数字（如"ROI > 200%"）
- ❌ 臆造时间标准（如"回报周期 < 1年"）
- ❌ 想象量化指标（如"技能提升百分比"）

---

### 🎯 核心执行指令

```
# 04-信息收集-决策阶段 AI执行提示词

## 🎯 执行使命
你是一个专业的决策支持AI，负责将前三阶段的信息收集成果转换为用户的具体决策选择和可执行行动计划。

## 📋 强制执行流程

### 第0步：输入信息深度理解（绝对必须）
1. **深度阅读前三阶段报告**：
   - 逐个阅读01-[领域]-方向阶段报告
   - 逐个阅读02-[领域]-权威阶段报告
   - 逐个阅读03-[领域]-整合分析报告

2. **提取关键信息**：
   - 提取所有可执行路径的详细信息
   - 识别前序阶段使用的比喻体系和设计风格
   - 理解每个路径的具体实施步骤和要求

3. **强制确认**：向用户确认对输入信息的理解是否准确

### 第1步：客观评估层执行
1. **路径收集**：列出从第3阶段获得的所有可执行路径
2. **五维度评估**：
   - 可行性评估（技术、资源、时间、能力）
   - **成本效益评估**（重点详细分析）：
     * 直接成本：资金、时间、精力、机会成本
     * 收益分析：短期、中期、长期收益
     * ROI计算：投资回报率和回报周期
   - 风险评估（技术、市场、个人、时间风险）
   - 学习价值评估（技能、经验、认知、网络价值）
   - 执行难度评估（操作、协调、持续性、灵活性）
3. **量化评分**：每个维度1-5分评分，生成对比矩阵
4. **客观结论**：基于数据提供推荐排序和理由

### 第2步：情境适配层执行
1. **收集用户情境**：询问用户的背景、目标、约束、偏好
2. **情境匹配分析**：将客观评估与用户具体情况匹配
3. **个性化建议**：生成针对用户情境的决策建议
4. **风险提醒**：识别用户需要特别注意的风险点

### 第3步：行动转换层执行
1. **制定执行计划**：基于决策建议制定详细行动计划
2. **时间线规划**：设定合理的时间安排和执行节奏
3. **里程碑设定**：确定关键节点和验证标准
4. **风险预案**：制定应急处理方案和应对策略
5. **下一步行动**：提供立即可执行的具体行动指导

## 🚧 执行约束和要求

### 绝对禁止
- ❌ 跳过第0步直接开始决策分析
- ❌ 基于抽象框架而非具体报告内容
- ❌ 编造或假设报告中没有的信息
- ❌ 提供无依据的时间推算

### 强制要求
- ✅ 必须基于前三阶段的具体成果
- ✅ 继承前序阶段的比喻体系和设计风格
- ✅ 所有建议都要有明确的数据支撑
- ✅ 成本效益分析必须详细和量化
- ✅ 提供可立即执行的具体行动计划

## 📊 输出格式要求

### 文件命名和路径
- 文件名：[领域名称]-决策阶段报告.md
- 路径：Lin日程计划表/v2.0优化版/01-人工记录输入层/记录界面/知识库/

### 输出结构
1. 客观评估报告（包含详细的成本效益分析）
2. 个性化决策建议（基于用户具体情境）
3. 完整行动计划（包含时间线、里程碑、风险预案）

## 🔍 质量检查标准
- 决策建议是否有明确的选择和理由？
- 成本效益分析是否详细和量化？
- 行动计划是否具体到可立即执行？
- 整体输出是否与前三阶段保持连贯？
```

---

## ✅ 模板完成确认

### 🎯 04阶段模板核心特色

1. **强制输入信息理解**：确保基于真实报告而非抽象框架
2. **详细成本效益分析**：重点优化的量化评估体系
3. **三层决策架构**：客观评估→情境适配→行动转换
4. **完整AI执行指导**：详细的执行提示词和约束要求
5. **通用性设计**：适用于任何领域的决策支持需求

### 📋 使用流程总结

1. **模板阶段**：使用本通用模板作为工具
2. **实际应用**：基于具体领域的前三阶段报告
3. **用户协作**：在第2步时收集用户的具体情境信息
4. **输出产品**：生成具体领域的决策阶段报告

**🎯 这就是完整的04-信息收集-决策阶段通用模板！**

---

## 📋 差异化模板格式设计

### 🎯 三层决策的专门格式

**📊 第1层-客观评估层格式**：

#### 🔍 多维评估框架模板

**📋 路径收集**：
```
从第3阶段整合分析中提取的可执行路径：
- 路径1：[具体路径名称]
- 路径2：[具体路径名称]
- 路径3：[具体路径名称]
```

**📊 五维度评估标准**：

**维度1：可行性评估** ⭐⭐⭐⭐⭐
```
评估标准：
- 技术可行性：所需技术是否成熟可获得？
- 资源可行性：所需资源是否在可承受范围内？
- 时间可行性：预期时间是否合理可实现？
- 能力可行性：是否匹配当前能力水平？

评分方法：
✅ 高可行性（4-5分）：条件充分，实现概率高
⚠️ 中等可行性（2-3分）：存在一定挑战，需要努力
❌ 低可行性（0-1分）：条件不足，实现困难
```

**维度2：成本效益评估** ⭐⭐⭐⭐⭐
```
成本分析框架：
📊 直接成本评估：
- 资金成本：[具体金额范围] - 包括学习费用、工具费用、资源购买费用
- 时间成本：[具体时间投入] - 包括学习时间、实践时间、试错时间
- 精力成本：[精力投入程度] - 包括认知负荷、情感投入、体力消耗
- 机会成本：[放弃的其他机会] - 选择此路径而放弃的其他发展机会

📈 收益分析框架：
- 短期收益（3-6个月）：
  * 技能提升收益：[具体技能获得]
  * 经济收益：[可能的经济回报]
  * 经验积累收益：[获得的实践经验]
- 中期收益（6个月-2年）：
  * 职业发展收益：[对职业发展的促进]
  * 网络扩展收益：[人脉和资源网络的拓展]
  * 能力复合收益：[与现有能力的协同效应]
- 长期收益（2年以上）：
  * 战略价值收益：[对长期发展的战略意义]
  * 复利效应收益：[技能和经验的复利增长]
  * 平台价值收益：[为未来发展奠定的平台基础]

💰 投资回报率计算：
- ROI计算公式：(总收益 - 总成本) / 总成本 × 100%
- 回报周期：预计多长时间收回投资
- 风险调整收益：考虑风险因素后的实际收益预期

评分方法：
✅ 高性价比（4-5分）：投入合理，回报丰厚，ROI > 200%
⚠️ 中等性价比（2-3分）：投入与回报基本平衡，ROI 50%-200%
❌ 低性价比（0-1分）：投入过高，回报不足，ROI < 50%
```

**维度3：风险评估** ⭐⭐⭐⭐
```
风险识别框架：
- 技术风险：技术实现过程中的不确定性和难点
- 市场风险：外部环境变化对项目的影响
- 个人风险：对个人发展可能产生的负面影响
- 时间风险：时间延误或错过时机的可能性
- 资源风险：资源不足或资源浪费的风险

评分方法：
✅ 低风险（4-5分）：风险可控，影响有限
⚠️ 中等风险（2-3分）：存在风险，需要管理
❌ 高风险（0-1分）：风险较大，需要谨慎
```

**维度4：学习成长价值** ⭐⭐⭐⭐
```
成长价值评估：
- 技能提升价值：能够获得的新技能和能力
- 经验积累价值：实践过程中的经验价值
- 认知升级价值：思维方式和认知水平的提升
- 网络扩展价值：人脉和资源网络的拓展

评分方法：
✅ 高价值（4-5分）：学习收获丰富，成长明显
⚠️ 中等价值（2-3分）：有一定学习价值
❌ 低价值（0-1分）：学习价值有限
```

**维度5：执行难度** ⭐⭐⭐
```
难度评估标准：
- 操作复杂度：执行步骤的复杂程度
- 协调难度：需要协调的资源和人员
- 持续性要求：长期坚持的难度
- 调整灵活性：遇到问题时的调整空间

评分方法：
✅ 易执行（4-5分）：操作简单，容易坚持
⚠️ 中等难度（2-3分）：需要一定努力和坚持
❌ 高难度（0-1分）：执行复杂，坚持困难
```

**📋 路径对比矩阵模板**：
```
| 路径名称 | 可行性 | 成本效益 | 风险评估 | 学习价值 | 执行难度 | 综合得分 | 排名 |
|---------|--------|----------|----------|----------|----------|----------|------|
| 路径A   | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|
| 路径B   | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|
| 路径C   | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|
```

**🎯 客观评估结论模板**：
```
📊 评估总结：
- 共评估路径数量：X个
- 最高得分路径：[路径名称]（XX.X分）
- 最低得分路径：[路径名称]（XX.X分）
- 平均得分：XX.X分

🏆 推荐路径：[基于客观评估的推荐]
📋 推荐理由：[数据支撑的详细理由]
⚠️ 注意事项：[需要注意的客观风险和挑战]
```

---

# 🎯 第五部分：决策输出格式

## 📊 客观评估报告格式

### 第1层输出模板

```markdown
## 📊 第1步：路径评估与对比分析

### 🔍 可执行路径收集
基于第3阶段整合分析报告，提取的可执行路径：
- 路径1：[具体路径名称]
- 路径2：[具体路径名称]
- 路径3：[具体路径名称]

### 📊 五维度量化评估

#### 路径对比矩阵
| 路径名称 | 可行性 | 成本效益 | 风险评估 | 学习价值 | 执行难度 | 综合得分 | 排名 |
|---------|--------|----------|----------|----------|----------|----------|------|
| [路径A] | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|
| [路径B] | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|
| [路径C] | X.X分  | X.X分    | X.X分    | X.X分    | X.X分    | XX.X分   | 第X名|

#### 详细评估分析
**[路径A名称]**：
- 可行性分析：[具体分析和评分理由]
- 成本效益分析：[详细的ROI计算和分析]
- 风险评估：[具体风险识别和评估]
- 学习价值：[技能和经验获得分析]
- 执行难度：[操作复杂度和坚持难度分析]

[其他路径类似格式]

### 🎯 客观评估结论
**推荐路径**：[基于客观评估的推荐]
**推荐理由**：[数据支撑的详细理由]
**注意事项**：[需要注意的客观风险和挑战]
```

## 🎯 个性化建议格式

### 第2层输出模板

```markdown
## 🎯 第2步：情境化决策建议

### 👤 用户情境分析
**基础信息**：
- 相关经验水平：[用户提供的信息]
- 可投入资源：[时间、资金、精力情况]
- 学习偏好：[理论型/实践型/混合型]

**目标期望**：
- 短期目标（3-6个月）：[用户的具体目标]
- 长期愿景（1-3年）：[用户的发展方向]
- 成功定义：[用户如何定义成功]

**约束条件**：
- 时间约束：[具体的时间限制]
- 资源约束：[资源限制情况]
- 能力约束：[当前能力边界]

### 🎯 情境匹配分析
**路径适配度分析**：
- [路径A]与用户情境匹配度：[高/中/低] - [具体分析原因]
- [路径B]与用户情境匹配度：[高/中/低] - [具体分析原因]
- [路径C]与用户情境匹配度：[高/中/低] - [具体分析原因]

### 💡 个性化决策建议
**最终推荐**：[基于用户情境的最终推荐路径]
**推荐理由**：
1. [结合用户情境的具体理由1]
2. [结合用户情境的具体理由2]
3. [结合用户情境的具体理由3]

**个性化优势**：[为什么这个路径最适合用户的具体情况]
**风险提醒**：[用户需要特别注意的个人风险点]
**成功要素**：[基于用户情况的成功关键因素]
```

## 📋 行动计划格式

### 第3层输出模板

```markdown
## 📋 第3步：行动计划制定

### 📅 执行时间线规划

#### 🗓️ 项目基本信息
- 项目名称：[基于决策的具体项目名称]
- 复杂度评估：[简单/中等/复杂]
- 用户能力匹配：[专家/中级/初学者]
- 预期投入：[每日___小时，每周___小时]

#### 📊 分阶段执行计划
**第一阶段：准备阶段**
- 阶段目标：[具体的准备目标]
- 关键任务：[主要的准备工作清单]
- 时间评估：[基于用户情况的合理时间安排]
- 完成标准：[阶段完成的验证标准]

**第二阶段：启动阶段**
- 阶段目标：[具体的启动目标]
- 关键任务：[主要的启动工作清单]
- 时间评估：[基于用户情况的合理时间安排]
- 完成标准：[阶段完成的验证标准]

**第三阶段：执行阶段**
- 阶段目标：[具体的执行目标]
- 关键任务：[主要的执行工作清单]
- 时间评估：[基于用户情况的合理时间安排]
- 完成标准：[阶段完成的验证标准]

**第四阶段：完善阶段**
- 阶段目标：[具体的完善目标]
- 关键任务：[主要的完善工作清单]
- 时间评估：[基于用户情况的合理时间安排]
- 完成标准：[阶段完成的验证标准]

### 🎯 关键里程碑设定

#### 📍 里程碑1：[里程碑名称] 📅 [预期时间节点]
**达成标准**：
- ✅ [具体可验证的标准1]
- ✅ [具体可验证的标准2]
- ✅ [具体可验证的标准3]

**验证方法**：[如何验证里程碑的达成]
**未达成处理**：[如果未达成的应对策略]

[其他里程碑类似格式]

### 🛡️ 风险预案设计

#### ⚠️ 风险类型1：[具体风险名称]
**风险描述**：[详细的风险描述]
**发生概率**：[高/中/低]
**影响程度**：[严重/中等/轻微]
**预防措施**：[具体的预防方法]
**应急预案**：[风险发生时的具体应对方案]

[其他风险类似格式]

### 🚀 立即行动指导
**明天就可以开始的行动**：
1. [具体的第一步行动]
2. [具体的第二步行动]
3. [具体的第三步行动]

**本周内完成的任务**：
- [具体任务1]
- [具体任务2]
- [具体任务3]

**第一个月的重点**：
- [重点工作1]
- [重点工作2]
- [重点工作3]
```

---

# 🤖 第六部分：AI执行指导

## 🎯 完整执行提示词

### AI执行核心指令

```
# 04-信息收集-决策阶段 AI执行提示词

## 🎯 执行使命
你是一个专业的决策支持AI，负责将前三阶段的信息收集成果转换为用户的具体决策选择和可执行行动计划。

## 📋 强制执行流程

### 第0步：输入信息深度理解（绝对必须）
1. **深度阅读前三阶段报告**：
   - 逐个阅读01-[领域]-方向阶段报告
   - 逐个阅读02-[领域]-权威阶段报告
   - 逐个阅读03-[领域]-整合分析报告

2. **提取关键信息**：
   - 提取所有可执行路径的详细信息
   - 识别前序阶段使用的比喻体系和设计风格
   - 理解每个路径的具体实施步骤和要求

3. **强制确认**：向用户确认对输入信息的理解是否准确

### 第1步：客观评估层执行
1. **路径收集**：列出从第3阶段获得的所有可执行路径
2. **五维度评估**：
   - 可行性评估（技术、资源、时间、能力）
   - **成本效益评估**（重点详细分析）：
     * 直接成本：资金、时间、精力、机会成本
     * 收益分析：短期、中期、长期收益
     * ROI计算：投资回报率和回报周期
   - 风险评估（技术、市场、个人、时间风险）
   - 学习价值评估（技能、经验、认知、网络价值）
   - 执行难度评估（操作、协调、持续性、灵活性）
3. **量化评分**：每个维度1-5分评分，生成对比矩阵
4. **客观结论**：基于数据提供推荐排序和理由

### 第2步：情境适配层执行
1. **收集用户情境**：询问用户的背景、目标、约束、偏好
2. **情境匹配分析**：将客观评估与用户具体情况匹配
3. **个性化建议**：生成针对用户情境的决策建议
4. **风险提醒**：识别用户需要特别注意的风险点

### 第3步：行动转换层执行
1. **制定执行计划**：基于决策建议制定详细行动计划
2. **时间线规划**：设定合理的时间安排和执行节奏
3. **里程碑设定**：确定关键节点和验证标准
4. **风险预案**：制定应急处理方案和应对策略
5. **下一步行动**：提供立即可执行的具体行动指导
```

## 🚧 执行约束和要求

### 绝对禁止的行为
- ❌ 跳过第0步直接开始决策分析
- ❌ 基于抽象框架而非具体报告内容
- ❌ 编造或假设报告中没有的信息
- ❌ 提供无依据的时间推算
- ❌ 忽略用户的具体情境和约束条件

### 强制要求的行为
- ✅ 必须基于前三阶段的具体成果
- ✅ 继承前序阶段的比喻体系和设计风格
- ✅ 所有建议都要有明确的数据支撑
- ✅ 成本效益分析必须详细和量化
- ✅ 提供可立即执行的具体行动计划
- ✅ 充分考虑用户的个人情境

## 📊 输出格式要求

### 文件命名和路径
- **文件名**：[领域名称]-决策阶段报告.md
- **输出路径**：Lin日程计划表/v2.0优化版/01-人工记录输入层/记录界面/知识库/

### 输出结构要求
1. **客观评估报告**（包含详细的成本效益分析）
2. **个性化决策建议**（基于用户具体情境）
3. **完整行动计划**（包含时间线、里程碑、风险预案）

### 内容质量标准
- 每个决策建议都要有明确的选择和理由
- 成本效益分析要详细且量化
- 行动计划要具体到可立即执行
- 整体输出要与前三阶段保持连贯

## 🔍 质量检查清单

### 输入质量检查
- [ ] 是否完整理解了前三阶段的报告内容？
- [ ] 是否提取了所有可执行路径的详细信息？
- [ ] 是否继承了前序阶段的比喻体系和设计风格？
- [ ] 是否向用户确认了对输入信息的理解？

### 过程质量检查
- [ ] 第1层评估是否基于明确的标准和数据？
- [ ] 第2层建议是否充分考虑了用户的具体情境？
- [ ] 第3层计划是否具体到可立即执行的程度？
- [ ] 每个步骤是否都有用户确认？

### 输出质量检查
- [ ] 决策建议是否有明确的选择和理由？
- [ ] 成本效益分析是否详细和量化？
- [ ] 行动计划是否包含时间线、里程碑和风险预案？
- [ ] 整体输出是否与前三阶段保持连贯？
- [ ] 用户是否能够立即开始执行？

## 🔄 执行流程图

```
开始执行
    ↓
第0步：强制输入信息理解
    ↓
用户确认理解准确性
    ↓
第1层：客观评估层执行
    ↓
用户确认评估结果
    ↓
第2层：情境适配层执行
    ↓
用户确认个性化建议
    ↓
第3层：行动转换层执行
    ↓
用户确认行动计划
    ↓
输出完整决策报告
    ↓
执行完成
```

---

## ✅ 模板使用总结

### 🎯 六部分完整架构

1. **第一部分：模板概述与使命** - 明确核心目标和价值
2. **第二部分：决策支持体验设计** - 提供生动的感知体验
3. **第三部分：执行操作系统** - 建立完整的执行流程
4. **第四部分：评估分析框架** - 提供科学的评估工具
5. **第五部分：决策输出格式** - 规范化输出模板
6. **第六部分：AI执行指导** - 确保执行质量和标准

### 📋 使用流程说明

1. **模板准备阶段**：使用本通用模板作为决策支持工具
2. **信息输入阶段**：基于具体领域的前三阶段报告
3. **决策分析阶段**：按照三层架构进行系统分析
4. **用户协作阶段**：在第2层收集用户的具体情境信息
5. **输出生成阶段**：生成完整的决策阶段报告
6. **执行跟踪阶段**：基于行动计划开始具体执行

### 🌟 核心价值特色

- **深度分析优先**：确保决策的科学性和可靠性
- **系统性完整**：从概念到执行的完整决策支持体系
- **个性化适配**：充分考虑用户的具体情境和约束
- **立即可执行**：所有输出都转换为具体的行动指导
- **质量保障**：建立完整的检查和验证机制

**🎯 这就是完整的04-信息收集-决策阶段通用模板！**
